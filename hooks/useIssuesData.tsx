import issues from "@/data/data.json";
import { useQuery } from "@tanstack/react-query";

interface UseIssuesDataOptions {
  simulateError?: boolean;
  simulateSlowNetwork?: boolean;
}

export function useIssuesData(options: UseIssuesDataOptions = {}) {
  const { simulateError = false, simulateSlowNetwork = false } = options;

  return useQuery({
    queryKey: ["issues", { simulateError, simulateSlowNetwork }],
    queryFn: async () => {
      // Simulate network delay
      const delay = simulateSlowNetwork ? 2000 : 300;
      await new Promise((res) => setTimeout(res, delay));

      // Simulate error for testing
      if (simulateError) {
        throw new Error("Failed to fetch issues. Please check your connection and try again.");
      }

      return issues;
    },
    staleTime: 1000 * 60 * 5,
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
}
