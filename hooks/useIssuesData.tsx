import issues from "@/data/data.json";
import { useQuery } from "@tanstack/react-query";

interface UseIssuesDataOptions {
  simulateError?: boolean;
  simulateSlowNetwork?: boolean;
}

/**
 * Custom hook to fetch issues data with optional error/slow network simulation
 * Useful for testing loading states and error handling
 */
export function useIssuesData(options: UseIssuesDataOptions = {}) {
  const { simulateError = false, simulateSlowNetwork = false } = options;

  return useQuery({
    queryKey: ["issues", { simulateError, simulateSlowNetwork }],
    queryFn: async () => {
      // Add artificial delay to simulate real API calls
      const networkDelay = simulateSlowNetwork ? 2000 : 300;
      await new Promise((resolve) => setTimeout(resolve, networkDelay));

      // Throw error for testing error states
      if (simulateError) {
        throw new Error("Failed to fetch issues. Please check your connection and try again.");
      }

      // Return the mock data
      return issues;
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
    retry: 3, // Retry failed requests 3 times
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000), // Exponential backoff
  });
}
