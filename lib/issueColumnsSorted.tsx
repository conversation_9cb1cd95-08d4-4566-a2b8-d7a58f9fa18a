// src/lib/issueColumns.tsx
import {ColumnDef} from "@tanstack/react-table";
import {ArrowUpDown, ArrowUp, ArrowDown} from "lucide-react";
import {Button} from "@/components/ui/button";
import { useAtom } from 'jotai';
import { sortAtom, SortField } from '@/store/tableStore';

// Priority order is handled in useFilteredData hook

// Helper function to create sortable header with <PERSON><PERSON>
const createSortableHeader = (title: string, field: SortField) => {
    const SortableHeaderComponent = () => {
    const [sort, setSort] = useAtom(sortAtom);
    const isActive = sort.field === field;
    const direction = isActive ? sort.direction : 'asc';

    const handleSort = () => {
        if (isActive) {
            // Toggle direction if same field
            setSort({
                field,
                direction: sort.direction === 'asc' ? 'desc' : 'asc'
            });
        } else {
            // Set new field with asc direction
            setSort({ field, direction: 'asc' });
        }
    };

    return (
        <Button
            variant="ghost"
            onClick={handleSort}
            className="h-auto p-0 font-medium hover:bg-transparent"
        >
            {title}
            {isActive && direction === "asc" ? (
                <ArrowUp className="ml-2 h-4 w-4"/>
            ) : isActive && direction === "desc" ? (
                <ArrowDown className="ml-2 h-4 w-4"/>
            ) : (
                <ArrowUpDown className="ml-2 h-4 w-4"/>
            )}
        </Button>
    );
    };

    SortableHeaderComponent.displayName = `SortableHeader_${field}`;
    return SortableHeaderComponent;
};

type Issue = {
    identifier: string;
    title: string;
    labels: string[];
    project: string;
    assignee: string | null;
    dueDate: string;
    status: string;
    priority: string;
    description: string;
    createdAt: string;
    updatedAt: string;
    cycle: string;
    estimate: number;
};

export const issueColumns: ColumnDef<Issue>[] = [
    {
        accessorKey: "identifier",
        header: "Identifier",
    },
    {
        accessorKey: "title",
        header: "Title",
    },
    {
        accessorKey: "labels",
        header: "Labels",
    },
    {
        accessorKey: "project",
        header: "Project",
        cell: ({row}) => row.original.project || "-",
    },
    {
        accessorKey: "assignee",
        header: "Assignee",
        cell: ({row}) => row.original.assignee || "-",
        enableGrouping: true,
    },
    {
        accessorKey: "cycle",
        header: "Cycle",
        enableGrouping: true,
    },
    {
        accessorKey: "priority",
        header: createSortableHeader("Priority", "priority"),
        enableGrouping: true,
    },
    {
        accessorKey: "status",
        header: "Status",
        enableGrouping: true,
    },
    {
        accessorKey: "estimate",
        header: createSortableHeader("Estimate", "estimate"),
        enableGrouping: true,
    },
    {
        accessorKey: "createdAt",
        header: createSortableHeader("Created At", "createdAt"),
        cell: (info) => new Date(info.getValue() as string).toLocaleDateString(),
    },
    {
        accessorKey: "dueDate",
        header: createSortableHeader("Due Date", "dueDate"),
        cell: (info) => new Date(info.getValue() as string).toLocaleDateString(),
    },
    {
        accessorKey: "updatedAt",
        header: createSortableHeader("Updated At", "updatedAt"),
        cell: (info) => new Date(info.getValue() as string).toLocaleDateString(),
    },
];
