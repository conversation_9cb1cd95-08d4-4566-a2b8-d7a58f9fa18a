# Issue Dashboard

A responsive issue tracking dashboard with advanced table features, built for a technical interview.

## 🚀 Live Demo

**[View Live Demo](https://issue-dashboard-nine.vercel.app/)** - Deployed on Vercel

## Quick Start

```bash
npm install
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to view it locally.

## What I Built

**Main Stack:**
- Next.js 15 + React 19 + TypeScript
- Tailwind CSS + shadcn/ui components
- TanStack Table for data tables
- Jotai for state management
- React Query for data fetching

**Key Features:**
- Advanced table with sorting, filtering, grouping, pagination
- Skeleton loading states and error handling
- Dark/light theme support
- Responsive design
- Demo controls for testing different states

## Tech Choices & Why

**TanStack Table** - Needed a robust table solution that could handle complex sorting/filtering without reinventing the wheel. Headless approach gives full control over UI.

**Jotai** - Wanted something lighter than Redux but more powerful than useState. Atomic approach prevents unnecessary re-renders and the persistence utilities are great for user settings.

**React Query** - Handles all the data fetching complexity (caching, retries, background updates) that I didn't want to build myself.

**shadcn/ui** - High-quality components that look professional out of the box. Built on Radix so accessibility is handled.

## Project Structure

Followed atomic design principles:

```
app/components/
├── molecules/     # TableSkeleton, ErrorBanner, LoadingSpinner
├── organisms/     # GenericTable, Header, TableRowRenderer
└── templates/     # IssuesTable (main table component)

hooks/             # useIssuesData, useFilteredData, useIssuesTable
store/             # Jotai atoms for state management
```

**State Management:**
- Global state (Jotai): theme, table settings, filters
- Local state: component-specific stuff like form inputs
- Persistence: localStorage for user preferences

## What Makes It Special

**Loading States** - Spent time making realistic skeleton loaders that match the actual table structure. Way better UX than just showing a spinner.

**Error Handling** - Built proper error boundaries with retry functionality. Includes demo controls to test error states.

**Performance** - Memoized filter functions, debounced search, client-side pagination. Should handle large datasets well.

**Accessibility** - Used shadcn/ui components which are built on Radix, so keyboard navigation and screen readers work properly.

## Demo Features

Added some demo controls to test different states:
- **Error simulation** - Toggle to see error handling in action
- **Slow network** - Test loading states with artificial delays
- **Theme toggle** - Switch between light/dark modes

These make it easy to demo the app's resilience during interviews.

## Notes

Built this as a showcase of modern React patterns. Focused on:
- Clean, maintainable code
- Good user experience (loading states, error handling)
- Performance considerations
- Accessibility best practices

The demo controls make it easy to show off different states during interviews. Remove the demo section for production use.
