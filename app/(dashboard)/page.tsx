"use client";

import { useAtomValue } from 'jotai';
import { groupByAtom } from '@/store/tableStore';
import SearchInput from "@/app/components/molecules/SearchInput";
import GroupByDropdown from "@/app/components/molecules/GroupByDropdown";
import FiltersBar from "@/app/components/molecules/FiltersBar";
import PaginationControls from "@/app/components/molecules/PaginationControls";
import IssuesTable from "@/app/components/templates/IssuesTable";
import IssueDetailModal from "@/app/components/organisms/IssueDetailModal";
import TableSkeleton from "@/app/components/molecules/TableSkeleton";
import ErrorBanner from "@/app/components/molecules/ErrorBanner";
import LoadingSpinner from "@/app/components/molecules/LoadingSpinner";
import { useIssuesData } from "@/hooks/useIssuesData";
import { useFilteredData } from "@/hooks/useFilteredData";
import { Button } from "@/components/ui/button";
import { AlertTriangle, Wifi, WifiOff } from "lucide-react";
import { useState } from "react";

export default function IssuesPage() {
    const [showErrorDemo, setShowErrorDemo] = useState(false);
    const [showSlowNetworkDemo, setShowSlowNetworkDemo] = useState(false);
    const [isDismissed, setIsDismissed] = useState(false);

    const {
        data: issues = [],
        isLoading,
        error,
        refetch,
        isFetching
    } = useIssuesData({
        simulateError: showErrorDemo,
        simulateSlowNetwork: showSlowNetworkDemo
    });

    const groupBy = useAtomValue(groupByAtom);
    const paginationResult = useFilteredData(issues);

    // Show error banner if there's an error
    const renderError = () => {
        if (!error || isDismissed) return null;

        return (
            <ErrorBanner
                title="Failed to Load Issues"
                message={error instanceof Error ? error.message : "An unexpected error occurred"}
                onRetry={() => {
                    setIsDismissed(false);
                    refetch();
                }}
                onDismiss={() => setIsDismissed(true)}
            />
        );
    };

    // Reset dismissed state when toggling error demo
    const handleErrorToggle = () => {
        setShowErrorDemo(!showErrorDemo);
        setIsDismissed(false);
    };

    // Show loading skeleton during initial load
    if (isLoading) {
        return (
            <div className="space-y-6 py-6">
                <div className="space-y-4 px-4">
                    <div className="flex gap-4 flex-wrap items-center">
                        <SearchInput />
                        <GroupByDropdown />
                    </div>
                    {/* Show skeleton for filters bar */}
                    <div className="flex gap-2">
                        <div className="h-10 w-32 bg-muted animate-pulse rounded-md" />
                        <div className="h-10 w-32 bg-muted animate-pulse rounded-md" />
                        <div className="h-10 w-32 bg-muted animate-pulse rounded-md" />
                    </div>
                </div>
                <div className="px-4 space-y-4">
                    <TableSkeleton rows={10} />
                    {/* Pagination skeleton */}
                    <div className="flex items-center justify-between">
                        <div className="h-10 w-48 bg-muted animate-pulse rounded-md" />
                        <div className="flex gap-2">
                            <div className="h-10 w-10 bg-muted animate-pulse rounded-md" />
                            <div className="h-10 w-10 bg-muted animate-pulse rounded-md" />
                            <div className="h-10 w-10 bg-muted animate-pulse rounded-md" />
                            <div className="h-10 w-10 bg-muted animate-pulse rounded-md" />
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    // Handle case where pagination result might be undefined during hydration
    if (!paginationResult || !paginationResult.data) {
        return (
            <div className="p-4 text-center">
                <LoadingSpinner text="Preparing data..." />
            </div>
        );
    }

    return (
        <div className="space-y-6 py-6">
            {/* Demo Controls - Remove in production */}
            <div className="px-4">
                <div className="flex gap-2 p-3 bg-muted/50 rounded-lg border border-dashed">
                    <span className="text-sm text-muted-foreground mr-2">Demo Controls:</span>
                    <Button
                        variant={showErrorDemo ? "destructive" : "outline"}
                        size="sm"
                        onClick={handleErrorToggle}
                        className="h-7 text-xs"
                    >
                        <AlertTriangle className="h-3 w-3 mr-1" />
                        {showErrorDemo ? 'Hide' : 'Show'} Error
                    </Button>
                    <Button
                        variant={showSlowNetworkDemo ? "default" : "outline"}
                        size="sm"
                        onClick={() => setShowSlowNetworkDemo(!showSlowNetworkDemo)}
                        className="h-7 text-xs"
                    >
                        {showSlowNetworkDemo ? (
                            <><WifiOff className="h-3 w-3 mr-1" />Normal</>
                        ) : (
                            <><Wifi className="h-3 w-3 mr-1" />Slow</>
                        )} Network
                    </Button>
                </div>
            </div>

            {/* Error Banner */}
            <div className="px-4">
                {renderError()}
            </div>

            <div className="space-y-4 px-4">
                <div className="flex gap-4 flex-wrap items-center">
                    <SearchInput />
                    <GroupByDropdown />
                    {/* Show loading indicator when refetching */}
                    {isFetching && !isLoading && (
                        <LoadingSpinner size="sm" text="Refreshing..." />
                    )}
                </div>
                <FiltersBar data={issues} />
            </div>

            <div className="px-4 space-y-4">
                <IssuesTable data={paginationResult.data} groupBy={groupBy} />
                <PaginationControls
                    totalCount={paginationResult.totalCount}
                    pageCount={paginationResult.pageCount}
                    currentPage={paginationResult.currentPage}
                    hasNextPage={paginationResult.hasNextPage}
                    hasPreviousPage={paginationResult.hasPreviousPage}
                />
            </div>

            <IssueDetailModal issues={issues} />
        </div>
    );
}
