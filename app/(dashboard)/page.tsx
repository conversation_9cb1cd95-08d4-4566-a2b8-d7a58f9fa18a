"use client";

/**
 * Main dashboard page for the issue tracker
 * Features: table with sorting/filtering, loading states, error handling
 * TODO: Add bulk actions, export functionality
 */

import { useAtomValue } from 'jotai';
import { groupByAtom } from '@/store/tableStore';
import SearchInput from "@/app/components/molecules/SearchInput";
import GroupByDropdown from "@/app/components/molecules/GroupByDropdown";
import FiltersBar from "@/app/components/molecules/FiltersBar";
import PaginationControls from "@/app/components/molecules/PaginationControls";
import IssuesTable from "@/app/components/templates/IssuesTable";
import IssueDetailModal from "@/app/components/organisms/IssueDetailModal";
import TableSkeleton from "@/app/components/molecules/TableSkeleton";
import ErrorBanner from "@/app/components/molecules/ErrorBanner";
import LoadingSpinner from "@/app/components/molecules/LoadingSpinner";
import { useIssuesData } from "@/hooks/useIssuesData";
import { useFilteredData } from "@/hooks/useFilteredData";
import { Button } from "@/components/ui/button";
import { Alert<PERSON>riangle, Wifi, WifiOff } from "lucide-react";
import { useState } from "react";

export default function IssuesPage() {
    // Demo state for testing error and loading scenarios
    const [showErrorDemo, setShowErrorDemo] = useState(false);
    const [showSlowNetworkDemo, setShowSlowNetworkDemo] = useState(false);
    const [errorDismissed, setErrorDismissed] = useState(false);

    const {
        data: issues = [],
        isLoading,
        error,
        refetch,
        isFetching
    } = useIssuesData({
        simulateError: showErrorDemo,
        simulateSlowNetwork: showSlowNetworkDemo
    });

    const groupBy = useAtomValue(groupByAtom);
    const paginationResult = useFilteredData(issues);

    // Render error banner when there's an error and it hasn't been dismissed
    const renderErrorBanner = () => {
        if (!error || errorDismissed) return null;

        return (
            <ErrorBanner
                title="Failed to Load Issues"
                message={error instanceof Error ? error.message : "An unexpected error occurred"}
                onRetry={() => {
                    setErrorDismissed(false);
                    refetch();
                }}
                onDismiss={() => setErrorDismissed(true)}
            />
        );
    };

    // Toggle error demo and reset dismissed state
    const toggleErrorDemo = () => {
        setShowErrorDemo(!showErrorDemo);
        setErrorDismissed(false); // Show error banner again when toggling
    };

    // Show comprehensive loading skeleton while data is being fetched
    if (isLoading) {
        return (
            <div className="space-y-6 py-6">
                {/* Controls area with real components */}
                <div className="space-y-4 px-4">
                    <div className="flex gap-4 flex-wrap items-center">
                        <SearchInput />
                        <GroupByDropdown />
                    </div>
                    {/* Skeleton placeholders for filter dropdowns */}
                    <div className="flex gap-2">
                        <div className="h-10 w-32 bg-muted animate-pulse rounded-md" />
                        <div className="h-10 w-32 bg-muted animate-pulse rounded-md" />
                        <div className="h-10 w-32 bg-muted animate-pulse rounded-md" />
                    </div>
                </div>

                {/* Main content skeleton */}
                <div className="px-4 space-y-4">
                    <TableSkeleton rows={10} />

                    {/* Pagination controls skeleton */}
                    <div className="flex items-center justify-between">
                        <div className="h-10 w-48 bg-muted animate-pulse rounded-md" />
                        <div className="flex gap-2">
                            <div className="h-10 w-10 bg-muted animate-pulse rounded-md" />
                            <div className="h-10 w-10 bg-muted animate-pulse rounded-md" />
                            <div className="h-10 w-10 bg-muted animate-pulse rounded-md" />
                            <div className="h-10 w-10 bg-muted animate-pulse rounded-md" />
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    // Handle case where pagination result might be undefined during hydration
    if (!paginationResult || !paginationResult.data) {
        return (
            <div className="p-4 text-center">
                <LoadingSpinner text="Preparing data..." />
            </div>
        );
    }

    return (
        <div className="space-y-6 py-6">
            {/* Demo Controls - for testing different states during development */}
            <div className="px-4">
                <div className="flex gap-2 p-3 bg-muted/50 rounded-lg border border-dashed">
                    <span className="text-sm text-muted-foreground mr-2">Demo Controls:</span>
                    <Button
                        variant={showErrorDemo ? "destructive" : "outline"}
                        size="sm"
                        onClick={toggleErrorDemo}
                        className="h-7 text-xs"
                    >
                        <AlertTriangle className="h-3 w-3 mr-1" />
                        {showErrorDemo ? 'Hide' : 'Show'} Error
                    </Button>
                    <Button
                        variant={showSlowNetworkDemo ? "default" : "outline"}
                        size="sm"
                        onClick={() => setShowSlowNetworkDemo(!showSlowNetworkDemo)}
                        className="h-7 text-xs"
                    >
                        {showSlowNetworkDemo ? (
                            <><WifiOff className="h-3 w-3 mr-1" />Normal</>
                        ) : (
                            <><Wifi className="h-3 w-3 mr-1" />Slow</>
                        )} Network
                    </Button>
                </div>
            </div>

            {/* Error Banner */}
            <div className="px-4">
                {renderErrorBanner()}
            </div>

            <div className="space-y-4 px-4">
                <div className="flex gap-4 flex-wrap items-center">
                    <SearchInput />
                    <GroupByDropdown />
                    {/* Show subtle loading indicator during background refetch */}
                    {isFetching && !isLoading && (
                        <LoadingSpinner size="sm" text="Refreshing..." />
                    )}
                </div>
                <FiltersBar data={issues} />
            </div>

            <div className="px-4 space-y-4">
                <IssuesTable data={paginationResult.data} groupBy={groupBy} />
                <PaginationControls
                    totalCount={paginationResult.totalCount}
                    pageCount={paginationResult.pageCount}
                    currentPage={paginationResult.currentPage}
                    hasNextPage={paginationResult.hasNextPage}
                    hasPreviousPage={paginationResult.hasPreviousPage}
                />
            </div>

            <IssueDetailModal issues={issues} />
        </div>
    );
}
