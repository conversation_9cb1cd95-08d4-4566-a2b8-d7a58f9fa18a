"use client";
import {
    TableBody,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import {flexRender, Table as ReactTableInstance, Row} from "@tanstack/react-table";
import React from "react";

interface GenericTableProps<TData> {
    table: ReactTableInstance<TData>;
    rowRenderer: (row: Row<TData>, tableInstance: ReactTableInstance<TData>) => React.ReactNode;
}

export default function GenericTable<TData>({table, rowRenderer}: GenericTableProps<TData>) {
    return (
        <div className="rounded-md border">
            <div className="relative h-[32rem] overflow-auto">
                <table className="w-full caption-bottom text-sm min-w-[800px]">
                    <TableHeader className="sticky top-0 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/95 z-10 border-b shadow-sm">
                        {table.getHeaderGroups().map((headerGroup) => (
                            <TableRow key={headerGroup.id}>
                                {headerGroup.headers.map((header) => (
                                    <TableHead key={header.id}>
                                        {header.isPlaceholder
                                            ? null
                                            : flexRender(
                                                header.column.columnDef.header,
                                                header.getContext()
                                            )}
                                    </TableHead>
                                ))}
                            </TableRow>
                        ))}
                    </TableHeader>

                    <TableBody>
                        {table.getRowModel().rows.map((row) => (
                            <React.Fragment key={row.id}>
                                {rowRenderer(row, table)}
                            </React.Fragment>
                        ))}
                    </TableBody>
                </table>
            </div>
        </div>
    );
}