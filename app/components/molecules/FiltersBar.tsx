"use client";

import { use<PERSON>tom, useSet<PERSON>tom } from 'jotai';
import { filters<PERSON>tom, FilterState, resetPagination<PERSON>tom } from '@/store/tableStore';
import FilterDropdown from './FilterDropdown';
import { Button } from '@/components/ui/button';
import { X } from 'lucide-react';

type Issue = {
  identifier: string;
  title: string;
  labels: string[];
  project: string;
  assignee: string | null;
  dueDate: string;
  status: string;
  priority: string;
  description: string;
  createdAt: string;
  updatedAt: string;
  cycle: string;
  estimate: number;
};

interface FiltersBarProps {
  data: Issue[];
}

export default function FiltersBar({ data }: FiltersBarProps) {
  const [filters, setFilters] = useAtom(filtersAtom);
  const resetPagination = useSetAtom(resetPaginationAtom);

  // Extract unique values for each filter
  const uniqueStatuses = [...new Set(data.map(item => item.status).filter(Boolean))];
  const uniqueProjects = [...new Set(data.map(item => item.project).filter(<PERSON><PERSON>an))];
  const uniquePriorities = [...new Set(data.map(item => item.priority).filter(Boolean))];
  const uniqueAssignees = [...new Set(data.map(item => item.assignee).filter((assignee): assignee is string => Boolean(assignee)))];
  const uniqueCycles = [...new Set(data.map(item => item.cycle).filter(Boolean))];

  const updateFilter = (key: keyof FilterState, value: string) => {
    setFilters({ ...filters, [key]: value });
    resetPagination(); // Reset to first page when filter changes
  };

  const clearAllFilters = () => {
    setFilters({
      status: "",
      project: "",
      priority: "",
      assignee: "",
      cycle: "",
    });
    resetPagination(); // Reset to first page when clearing filters
  };

  const hasActiveFilters = Object.values(filters).some(value => value !== "");

  return (
    <div className="flex flex-wrap gap-3 items-center">
      <FilterDropdown
        value={filters.status}
        onChange={(value) => updateFilter('status', value)}
        options={uniqueStatuses}
        placeholder="Status"
      />

      <FilterDropdown
        value={filters.project}
        onChange={(value) => updateFilter('project', value)}
        options={uniqueProjects}
        placeholder="Project"
      />

      <FilterDropdown
        value={filters.priority}
        onChange={(value) => updateFilter('priority', value)}
        options={uniquePriorities}
        placeholder="Priority"
      />

      <FilterDropdown
        value={filters.assignee}
        onChange={(value) => updateFilter('assignee', value)}
        options={uniqueAssignees}
        placeholder="Assignee"
      />

      <FilterDropdown
        value={filters.cycle}
        onChange={(value) => updateFilter('cycle', value)}
        options={uniqueCycles}
        placeholder="Cycle"
      />

      {hasActiveFilters && (
        <Button
          variant="outline"
          size="sm"
          onClick={clearAllFilters}
          className="h-10"
        >
          <X className="h-4 w-4 mr-1" />
          Clear Filters
        </Button>
      )}
    </div>
  );
}
