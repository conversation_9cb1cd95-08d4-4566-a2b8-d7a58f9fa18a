"use client";

import { Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";

interface LoadingSpinnerProps {
  size?: "sm" | "md" | "lg";
  text?: string;
  className?: string;
}

// Size mappings for the spinner icon
const SPINNER_SIZES = {
  sm: "h-4 w-4",
  md: "h-6 w-6",
  lg: "h-8 w-8",
};

// Text size mappings to match spinner size
const TEXT_SIZES = {
  sm: "text-sm",
  md: "text-base",
  lg: "text-lg",
};

export default function LoadingSpinner({
  size = "md",
  text,
  className
}: LoadingSpinnerProps) {
  const spinnerSize = SPINNER_SIZES[size];
  const textSize = TEXT_SIZES[size];

  return (
    <div className={cn("flex items-center justify-center gap-2", className)}>
      <Loader2 className={cn("animate-spin", spinnerSize)} />
      {text && (
        <span className={cn("text-muted-foreground", textSize)}>
          {text}
        </span>
      )}
    </div>
  );
}
