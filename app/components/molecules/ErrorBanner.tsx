"use client";

import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { AlertTriangle, RefreshCw, X } from "lucide-react";
import { useState } from "react";

// Props for the error banner component
interface ErrorBannerProps {
  title?: string;
  message: string;
  onRetry?: () => void;
  onDismiss?: () => void;
  variant?: "default" | "destructive";
  showRetry?: boolean;
  showDismiss?: boolean;
}

export default function ErrorBanner({
  title = "Error",
  message,
  onRetry,
  onDismiss,
  variant = "destructive",
  showRetry = true,
  showDismiss = true,
}: ErrorBannerProps) {
  const [visible, setVisible] = useState(true);

  const handleDismissClick = () => {
    setVisible(false);
    onDismiss?.(); // Call parent dismiss handler if provided
  };

  // Don't render anything if dismissed
  if (!visible) return null;

  return (
    <Alert variant={variant} className="mb-6">
      <AlertTriangle className="h-4 w-4" />
      <div className="flex-1">
        <AlertTitle>{title}</AlertTitle>
        <AlertDescription className="mt-1">{message}</AlertDescription>
      </div>

      {/* Action buttons on the right */}
      <div className="flex items-center gap-2 ml-auto">
        {showRetry && onRetry && (
          <Button
            variant="outline"
            size="sm"
            onClick={onRetry}
            className="h-8"
          >
            <RefreshCw className="h-3 w-3 mr-1" />
            Retry
          </Button>
        )}

        {showDismiss && (
          <Button
            variant="ghost"
            size="sm"
            onClick={handleDismissClick}
            className="h-8 w-8 p-0"
            aria-label="Dismiss error"
          >
            <X className="h-3 w-3" />
            <span className="sr-only">Dismiss</span>
          </Button>
        )}
      </div>
    </Alert>
  );
}
