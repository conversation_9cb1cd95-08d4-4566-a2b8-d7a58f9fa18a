"use client";

import { Skeleton } from "@/components/ui/skeleton";
import { TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";

interface TableSkeletonProps {
  rows?: number;
}

// TODO: Maybe extract this to a constants file later?
const TABLE_COLUMNS = [
  "Identifier", "Title", "Labels", "Project", "Assignee",
  "Cycle", "Priority", "Status", "Estimate", "Created At", "Due Date", "Updated At"
];

// I spent way too much time getting these widths right 😅
const getHeaderWidth = (idx: number) => {
   const headerWidths = [
    "w-16", // Identifier - short
    "w-20", // Title - medium
    "w-16", // Labels - short
    "w-20", // Project
    "w-20", // Assignee
    "w-16", // Cycle
    "w-20", // Priority
    "w-16", // Status
    "w-20", // Estimate
    "w-24", // Created At - dates are longer
    "w-24", // Due Date
    "w-24", // Updated At
  ];
  return headerWidths[idx] ?? "w-20"; // fallback just in case
};

// Render different skeleton shapes based on column type
const renderCellSkeleton = (columnIndex: number) => {
  // Different columns need different skeleton shapes
  if (columnIndex === 0) {
    // Identifier - usually short like "ISS-123"
    return <Skeleton className="h-4 w-16" />;
  }

  if (columnIndex === 1) {
    // Title - can be pretty long
    return <Skeleton className="h-4 w-40" />;
  }

  if (columnIndex === 2) {
    // Labels - show multiple badge-like skeletons
    return (
      <div className="flex gap-1">
        <Skeleton className="h-5 w-12 rounded-full" />
        <Skeleton className="h-5 w-16 rounded-full" />
      </div>
    );
  }

  if (columnIndex === 3 || columnIndex === 4) {
    // Project & Assignee
    return <Skeleton className="h-4 w-20" />;
  }

  if (columnIndex === 5) {
    // Cycle - usually shorter
    return <Skeleton className="h-4 w-16" />;
  }

  if (columnIndex === 6 || columnIndex === 7) {
    // Priority & Status - badge-like
    return <Skeleton className="h-5 w-16 rounded-full" />;
  }

  if (columnIndex === 8) {
    // Estimate - just a number
    return <Skeleton className="h-4 w-12" />;
  }

  // Date columns (Created At, Due Date, Updated At)
  if (columnIndex >= 9) {
    return <Skeleton className="h-4 w-20" />;
  }

  // Fallback
  return <Skeleton className="h-4 w-16" />;
};

export default function TableSkeleton({ rows = 10 }: TableSkeletonProps) {
  return (
    <div className="rounded-md border">
      <div className="relative h-[32rem] overflow-auto">
        <table className="w-full caption-bottom text-sm min-w-[800px]">
          {/* Table header with skeleton placeholders */}
          <TableHeader className="sticky top-0 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/95 z-10 border-b shadow-sm">
            <TableRow>
              {TABLE_COLUMNS.map((_, idx) => (
                <TableHead key={`header-${idx}`}>
                  <Skeleton className={`h-4 ${getHeaderWidth(idx)}`} />
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>

          {/* Table body with realistic skeleton rows */}
          <TableBody>
            {Array.from({ length: rows }, (_, i) => (
              <TableRow key={`skeleton-row-${i}`}>
                {TABLE_COLUMNS.map((_, colIdx) => (
                  <TableCell key={`cell-${i}-${colIdx}`}>
                    {renderCellSkeleton(colIdx)}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </table>
      </div>
    </div>
  );
}
